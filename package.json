{"name": "agentic-cli-tool", "version": "1.0.0", "description": "Production-ready autonomous agentic AI-powered CLI tool system", "main": "dist/cli.js", "bin": {"agent-cli": "dist/cli.js"}, "scripts": {"build": "tsc", "dev": "tsx src/cli.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build", "start": "node dist/cli.js"}, "keywords": ["ai", "cli", "agent", "autonomous", "typescript", "llm", "tool"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=18.0.0"}, "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@google/generative-ai": "^0.15.0", "@mistralai/mistralai": "^0.4.0", "axios": "^1.6.8", "chalk": "^5.3.0", "commander": "^12.1.0", "conf": "^12.0.0", "dotenv": "^16.4.5", "enquirer": "^2.4.1", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "keytar": "^7.9.0", "listr2": "^8.2.1", "lodash": "^4.17.21", "nanoid": "^5.0.7", "node-fetch": "^3.3.2", "openai": "^4.52.7", "ora": "^8.0.1", "p-limit": "^5.0.0", "p-queue": "^8.0.1", "rxjs": "^7.8.1", "semver": "^7.6.2", "strip-ansi": "^7.1.0", "tree-kill": "^1.2.2", "uuid": "^10.0.0", "winston": "^3.13.0", "yargs": "^17.7.2", "zod": "^3.23.8"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.5", "@types/node": "^20.14.9", "@types/semver": "^7.5.8", "@types/uuid": "^10.0.0", "@types/yargs": "^17.0.32", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.3.2", "rimraf": "^5.0.7", "ts-jest": "^29.1.5", "tsx": "^4.15.7", "typescript": "^5.8.3"}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "git+https://github.com/yourusername/agentic-cli-tool.git"}, "bugs": {"url": "https://github.com/yourusername/agentic-cli-tool/issues"}, "homepage": "https://github.com/yourusername/agentic-cli-tool#readme"}
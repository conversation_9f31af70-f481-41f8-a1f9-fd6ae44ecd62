#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { createLogger, format, transports } from 'winston';
import { AgentCLI } from './ui/agent-cli';
import { ConfigManager } from './config/config-manager';
const packageJson = require('../package.json');
const version = packageJson.version;

const logger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  transports: [
    new transports.File({ filename: 'agent-cli-error.log', level: 'error' }),
    new transports.File({ filename: 'agent-cli.log' }),
  ],
});

// Add console transport for development
if (process.env['NODE_ENV'] !== 'production') {
  logger.add(new transports.Console({
    format: format.combine(
      format.colorize(),
      format.simple()
    )
  }));
}

async function main(): Promise<void> {
  const program = new Command();

  program
    .name('agent-cli')
    .description('Production-ready autonomous agentic AI-powered CLI tool system')
    .version(version);

  // Initialize configuration manager
  const configManager = new ConfigManager(logger);
  await configManager.initialize();

  // Load API keys from environment
  await configManager.loadFromEnvironment();

  // Interactive mode (default)
  program
    .command('interactive', { isDefault: true })
    .alias('i')
    .description('Start interactive agent session')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-s, --session <id>', 'Session ID to resume')
    .option('-v, --verbose', 'Enable verbose logging')
    .option('--debug', 'Enable debug logging')
    .option('--no-colors', 'Disable colored output')
    .action(async (options) => {
      try {
        // Update logging level
        if (options.debug) {
          logger.level = 'debug';
        } else if (options.verbose) {
          logger.level = 'verbose';
        }

        // Update UI config
        if (options.noColors) {
          await configManager.updateUIConfig({ colors: false });
        }

        const agentCLI = new AgentCLI(logger, configManager);
        await agentCLI.start({
          workingDirectory: options.directory,
          sessionId: options.session,
        });
      } catch (error) {
        logger.error('Failed to start interactive session', { error });
        console.error(chalk.red('Failed to start interactive session:'), error);
        process.exit(1);
      }
    });

  // Execute single goal
  program
    .command('execute <goal>')
    .alias('exec')
    .description('Execute a single goal autonomously')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-o, --output <format>', 'Output format (json|text)', 'text')
    .option('-t, --timeout <seconds>', 'Execution timeout in seconds', '300')
    .option('--dangerous', 'Allow dangerous operations')
    .action(async (goal, options) => {
      try {
        if (options.dangerous) {
          await configManager.updateToolsConfig({ allowDangerous: true });
        }

        const agentCLI = new AgentCLI(logger, configManager);
        const result = await agentCLI.executeGoal(goal, {
          workingDirectory: options.directory,
          timeout: parseInt(options.timeout) * 1000,
        });

        if (options.output === 'json') {
          console.log(JSON.stringify(result, null, 2));
        } else {
          if (result.success) {
            console.log(chalk.green('✓ Goal completed successfully'));
            if (result.data) {
              console.log(result.data);
            }
          } else {
            console.log(chalk.red('✗ Goal execution failed'));
            console.error(result.error);
            process.exit(1);
          }
        }
      } catch (error) {
        logger.error('Failed to execute goal', { error, goal });
        console.error(chalk.red('Failed to execute goal:'), error);
        process.exit(1);
      }
    });

  // Configuration commands
  const configCmd = program
    .command('config')
    .description('Manage configuration');

  configCmd
    .command('show')
    .description('Show current configuration')
    .option('--json', 'Output as JSON')
    .action(async (options) => {
      const config = configManager.getConfig();
      if (options.json) {
        console.log(JSON.stringify(config, null, 2));
      } else {
        console.log(chalk.blue('Current Configuration:'));
        console.log(JSON.stringify(config, null, 2));
      }
    });

  configCmd
    .command('set <key> <value>')
    .description('Set configuration value')
    .action(async (key, value) => {
      try {
        // Parse value as JSON if possible
        let parsedValue: unknown = value;
        try {
          parsedValue = JSON.parse(value);
        } catch {
          // Keep as string if not valid JSON
        }

        await configManager.updateConfig({ [key]: parsedValue });
        console.log(chalk.green(`✓ Configuration updated: ${key} = ${value}`));
      } catch (error) {
        console.error(chalk.red('Failed to update configuration:'), error);
        process.exit(1);
      }
    });

  configCmd
    .command('reset')
    .description('Reset configuration to defaults')
    .option('--confirm', 'Skip confirmation prompt')
    .action(async (options) => {
      if (!options.confirm) {
        console.log(chalk.yellow('This will reset all configuration to defaults.'));
        console.log(chalk.yellow('Use --confirm to proceed without this prompt.'));
        return;
      }

      try {
        await configManager.resetToDefaults();
        console.log(chalk.green('✓ Configuration reset to defaults'));
      } catch (error) {
        console.error(chalk.red('Failed to reset configuration:'), error);
        process.exit(1);
      }
    });

  // Provider management commands
  const providerCmd = program
    .command('provider')
    .description('Manage LLM providers');

  providerCmd
    .command('list')
    .description('List configured providers')
    .action(() => {
      const providers = configManager.getProviders();
      if (providers.length === 0) {
        console.log(chalk.yellow('No providers configured'));
        return;
      }

      console.log(chalk.blue('Configured Providers:'));
      providers.forEach(provider => {
        const status = provider.available ? chalk.green('✓') : chalk.red('✗');
        console.log(`${status} ${provider.name} (${provider.type}) - ${provider.model}`);
      });
    });

  providerCmd
    .command('add <name> <type> <model>')
    .description('Add a new provider')
    .option('--api-key <key>', 'API key')
    .option('--base-url <url>', 'Base URL')
    .option('--max-tokens <number>', 'Max tokens', '4000')
    .option('--temperature <number>', 'Temperature', '0.7')
    .option('--priority <number>', 'Priority', '1')
    .action(async (name, type, model, options) => {
      try {
        await configManager.addProvider({
          name,
          type: type as 'openai' | 'anthropic' | 'google' | 'mistral' | 'deepseek' | 'ollama',
          model,
          apiKey: options.apiKey,
          baseUrl: options.baseUrl,
          maxTokens: parseInt(options.maxTokens),
          temperature: parseFloat(options.temperature),
          priority: parseInt(options.priority),
          available: false,
        });
        console.log(chalk.green(`✓ Provider '${name}' added successfully`));
      } catch (error) {
        console.error(chalk.red('Failed to add provider:'), error);
        process.exit(1);
      }
    });

  providerCmd
    .command('remove <name>')
    .description('Remove a provider')
    .action(async (name) => {
      try {
        await configManager.removeProvider(name);
        console.log(chalk.green(`✓ Provider '${name}' removed successfully`));
      } catch (error) {
        console.error(chalk.red('Failed to remove provider:'), error);
        process.exit(1);
      }
    });

  // Session management commands
  const sessionCmd = program
    .command('session')
    .description('Manage sessions');

  sessionCmd
    .command('list')
    .description('List saved sessions')
    .option('--json', 'Output as JSON')
    .action(async (options) => {
      try {
        const { SessionManager } = await import('./core/session/session-manager');
        const sessionManager = new SessionManager(logger);
        const sessions = await sessionManager.listSessions();

        if (options.json) {
          console.log(JSON.stringify(sessions, null, 2));
        } else {
          if (sessions.length === 0) {
            console.log(chalk.yellow('No saved sessions found'));
            return;
          }

          console.log(chalk.blue('Saved Sessions:'));
          sessions.forEach(session => {
            const lastAccessed = new Date(session.lastAccessed).toLocaleString();
            console.log(`${chalk.green('•')} ${session.name} (${session.id})`);
            console.log(`  ${chalk.gray('Created:')} ${new Date(session.createdAt).toLocaleString()}`);
            console.log(`  ${chalk.gray('Last accessed:')} ${lastAccessed}`);
            console.log(`  ${chalk.gray('Working directory:')} ${session.context.workingDirectory}`);
            console.log();
          });
        }
      } catch (error) {
        console.error(chalk.red('Failed to list sessions:'), error);
        process.exit(1);
      }
    });

  sessionCmd
    .command('delete <id>')
    .description('Delete a session')
    .option('--confirm', 'Skip confirmation prompt')
    .action(async (id, options) => {
      try {
        const { SessionManager } = await import('./core/session/session-manager');
        const sessionManager = new SessionManager(logger);

        if (!options.confirm) {
          console.log(chalk.yellow(`This will permanently delete session: ${id}`));
          console.log(chalk.yellow('Use --confirm to proceed without this prompt.'));
          return;
        }

        await sessionManager.deleteSession(id);
        console.log(chalk.green(`✓ Session '${id}' deleted successfully`));
      } catch (error) {
        console.error(chalk.red('Failed to delete session:'), error);
        process.exit(1);
      }
    });

  sessionCmd
    .command('export <id> <path>')
    .description('Export a session to file')
    .action(async (id, path) => {
      try {
        const { SessionManager } = await import('./core/session/session-manager');
        const sessionManager = new SessionManager(logger);
        await sessionManager.exportSession(id, path);
        console.log(chalk.green(`✓ Session exported to: ${path}`));
      } catch (error) {
        console.error(chalk.red('Failed to export session:'), error);
        process.exit(1);
      }
    });

  sessionCmd
    .command('import <path>')
    .description('Import a session from file')
    .action(async (path) => {
      try {
        const { SessionManager } = await import('./core/session/session-manager');
        const sessionManager = new SessionManager(logger);
        const session = await sessionManager.importSession(path);
        console.log(chalk.green(`✓ Session imported: ${session.name} (${session.id})`));
      } catch (error) {
        console.error(chalk.red('Failed to import session:'), error);
        process.exit(1);
      }
    });

  // Health check command
  program
    .command('health')
    .description('Check system health and provider availability')
    .option('--detailed', 'Show detailed health information')
    .action(async (options) => {
      try {
        console.log(chalk.blue('🔍 Checking system health...\n'));

        // Check configuration
        console.log(chalk.cyan('Configuration:'));
        const validation = await configManager.validateConfig();
        if (validation.valid) {
          console.log(chalk.green('  ✓ Configuration is valid'));
        } else {
          console.log(chalk.red('  ✗ Configuration has errors:'));
          validation.errors?.forEach(error => console.log(`    - ${error}`));
        }

        // Check providers
        console.log(chalk.cyan('\nLLM Providers:'));
        const { LLMProviderManager } = await import('./core/providers/llm-provider-manager');
        const providerManager = new LLMProviderManager(logger);
        const providers = configManager.getProviders();

        if (providers.length === 0) {
          console.log(chalk.yellow('  ⚠️  No providers configured'));
        } else {
          await providerManager.initialize(providers);
          const healthResults = await providerManager.healthCheck();

          for (const provider of providers) {
            const isHealthy = healthResults[provider.name];
            const status = isHealthy ? chalk.green('✓') : chalk.red('✗');
            const apiKeyStatus = provider.apiKey ? chalk.green('configured') : chalk.red('missing');

            console.log(`  ${status} ${provider.name} (${provider.type})`);
            if (options.detailed) {
              console.log(`    Model: ${provider.model}`);
              console.log(`    API Key: ${apiKeyStatus}`);
              console.log(`    Base URL: ${provider.baseUrl || 'default'}`);
              console.log(`    Max Tokens: ${provider.maxTokens}`);
              console.log(`    Temperature: ${provider.temperature}`);
            }
          }
        }

        // Check file system permissions
        console.log(chalk.cyan('\nFile System:'));
        try {
          const testDir = './.agent-test';
          const fs = await import('fs/promises');
          await fs.mkdir(testDir, { recursive: true });
          await fs.writeFile(`${testDir}/test.txt`, 'test');
          await fs.readFile(`${testDir}/test.txt`);
          await fs.unlink(`${testDir}/test.txt`);
          await fs.rmdir(testDir);
          console.log(chalk.green('  ✓ Read/write permissions OK'));
        } catch (error) {
          console.log(chalk.red('  ✗ File system access failed:'), (error as Error).message);
        }

        // Check environment
        console.log(chalk.cyan('\nEnvironment:'));
        console.log(chalk.green(`  ✓ Node.js ${process.version}`));
        console.log(chalk.green(`  ✓ Platform: ${process.platform} (${process.arch})`));
        console.log(chalk.green(`  ✓ Working directory: ${process.cwd()}`));

        if (options.detailed) {
          console.log(`  Memory usage: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);
          console.log(`  Uptime: ${Math.round(process.uptime())}s`);
        }

        // Check dependencies
        console.log(chalk.cyan('\nDependencies:'));
        try {
          const packageJson = require('../package.json');
          const criticalDeps = ['commander', 'chalk', 'winston', 'zod'];

          for (const dep of criticalDeps) {
            if (packageJson.dependencies[dep]) {
              console.log(chalk.green(`  ✓ ${dep}`));
            } else {
              console.log(chalk.red(`  ✗ ${dep} missing`));
            }
          }
        } catch (error) {
          console.log(chalk.red('  ✗ Could not check dependencies'));
        }

        console.log(chalk.green('\n🎉 Health check completed!'));

      } catch (error) {
        console.error(chalk.red('Health check failed:'), error);
        process.exit(1);
      }
    });

  // Parse command line arguments
  await program.parseAsync(process.argv);
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', { error });
  console.error(chalk.red('Uncaught exception:'), error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection', { reason, promise });
  console.error(chalk.red('Unhandled rejection:'), reason);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\nReceived SIGINT, shutting down gracefully...'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\nReceived SIGTERM, shutting down gracefully...'));
  process.exit(0);
});

// Start the CLI
main().catch((error) => {
  logger.error('CLI startup failed', { error });
  console.error(chalk.red('CLI startup failed:'), error);
  process.exit(1);
});

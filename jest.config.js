module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      useESM: true,
    }],
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
    '!src/**/*.spec.ts',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@core/(.*)$': '<rootDir>/src/core/$1',
    '^@tools/(.*)$': '<rootDir>/src/tools/$1',
    '^@ui/(.*)$': '<rootDir>/src/ui/$1',
    '^@config/(.*)$': '<rootDir>/src/config/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@mistralai/mistralai$': '<rootDir>/src/__mocks__/@mistralai/mistralai.ts',
    '^@anthropic-ai/sdk$': '<rootDir>/src/__mocks__/@anthropic-ai/sdk.ts',
    '^@google/generative-ai$': '<rootDir>/src/__mocks__/@google/generative-ai.ts',
  },
  testTimeout: 30000,
  transformIgnorePatterns: [
    'node_modules/(?!(@mistralai|nanoid|@anthropic-ai|@google)/)',
  ],
  extensionsToTreatAsEsm: ['.ts'],
};
